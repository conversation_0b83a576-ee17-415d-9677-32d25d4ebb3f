#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

body {
  font-family: 'Inter', 'Segoe UI', <PERSON>l, sans-serif;
  background: #f7f8fa;
  margin: 0;
  padding: 0;
}

.app-container {
  max-width: 480px;
  margin: 40px auto;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.08);
  padding: 32px 24px;
}

.app-header {
  text-align: center;
  margin-bottom: 24px;
}

.logo-group {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 12px;
}

.logo {
  height: 48px;
  transition: transform 0.2s;
}
.logo:hover {
  transform: scale(1.1);
}

.app-title {
  font-size: 2rem;
  font-weight: 700;
  color: #222;
  margin: 0;
}

.card {
  background: #f3f6fa;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}

.input-row {
  display: flex;
  gap: 12px;
  margin-bottom: 18px;
}

.input {
  flex: 1;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #d1d5db;
  font-size: 1rem;
  outline: none;
  transition: border-color 0.2s;
}
.input:focus {
  border-color: #6366f1;
}

.btn {
  padding: 8px 16px;
  border-radius: 6px;
  border: none;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
  margin-left: 4px;
}

.btn-add {
  background: #6366f1;
  color: #fff;
}
.btn-add:hover {
  background: #4f46e5;
}

.btn-edit {
  background: #fbbf24;
  color: #222;
}
.btn-edit:hover {
  background: #f59e42;
}

.btn-delete {
  background: #ef4444;
  color: #fff;
}
.btn-delete:hover {
  background: #dc2626;
}

.btn-save {
  background: #10b981;
  color: #fff;
}
.btn-save:hover {
  background: #059669;
}

.btn-cancel {
  background: #d1d5db;
  color: #222;
}
.btn-cancel:hover {
  background: #9ca3af;
}

.item-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.item {
  background: #fff;
  border-radius: 8px;
  margin-bottom: 12px;
  padding: 12px 16px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  display: flex;
  align-items: center;
  transition: box-shadow 0.2s, transform 0.4s;
}

.item-row {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.item-text {
  flex: 1;
  font-size: 1.08rem;
  color: #222;
}

.edit-row {
  display: flex;
  gap: 8px;
  width: 100%;
}

.item-enter {
  animation: itemEnter 0.4s;
  box-shadow: 0 2px 8px rgba(99,102,241,0.12);
}
@keyframes itemEnter {
  from { transform: scale(0.95) translateY(10px); opacity: 0.2; }
  to { transform: scale(1) translateY(0); opacity: 1; }
}

.item-exit {
  animation: itemExit 0.4s forwards;
  opacity: 0.5;
}
@keyframes itemExit {
  to { transform: scale(0.95) translateY(10px); opacity: 0; }
}

.item-update {
  animation: itemUpdate 0.4s;
  box-shadow: 0 2px 8px rgba(16,185,129,0.12);
}
@keyframes itemUpdate {
  from { background: #d1fae5; }
  to { background: #fff; }
}

.read-the-docs {
  text-align: center;
  color: #6b7280;
  font-size: 0.95rem;
  margin-top: 32px;
}
