---
inclusion: always
---

# Product Overview

## Project Purpose
React ShuHaRi is a learning-focused todo application that demonstrates the progression of React development skills through the Japanese martial arts concept of Shu-Ha-Ri:
- **<PERSON> (守)**: Follow the rules - Basic React patterns and state management
- **Ha (破)**: Break the rules - Exploring different approaches and advanced patterns  
- **Ri (離)**: Transcend the rules - Mastering React architecture and best practices

## Target Users
- React developers learning state management patterns
- Students progressing from basic React to advanced concepts
- Developers exploring different todo app implementations
- Teams wanting to understand React/Redux integration patterns

## Key Features
- **Multiple Todo Implementations**: Different approaches to the same functionality
  - Basic React state management (App.jsx)
  - Reusable component patterns (TodoApp.jsx)
  - Redux Toolkit integration (store.js)
  - Panel-based organization (Panel.jsx)

- **Animation States**: Items have lifecycle states for smooth UX
  - `entering`: New items being added
  - `updating`: Items being modified
  - `exiting`: Items being removed
  - `idle`: Items in normal state

- **Modern Development Stack**: Latest React patterns and tooling
  - React 19 with modern hooks
  - Redux Toolkit for state management
  - Vite for fast development
  - Vitest for testing

## Business Objectives
- Demonstrate progressive React learning path
- Showcase different state management approaches
- Provide reusable patterns for todo-style applications
- Serve as a reference implementation for React best practices

## User Experience Goals
- Clean, intuitive interface with smooth animations
- Responsive design that works across devices
- Consistent interaction patterns across different implementations
- Visual feedback for all user actions through animation states

## Success Metrics
- Code clarity and maintainability
- Performance of state updates and animations
- Ease of adding new todo implementations
- Developer learning progression through different patterns
